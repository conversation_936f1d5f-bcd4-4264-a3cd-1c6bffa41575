import { useEffect, useState } from "react";
import { useQuery } from "@apollo/client";
import productCategoriesLanding from "../../../graphql/productCategoriesLanding.gql";
import MainLayout from "../../../component/globals/Layout";
import Breadcrumbs from "../../../component/globals/Breadcrumbs/Breadcrumbs"; // Add this line
import {
  PLContainer,
  PLHeading,
  PLProductGrid,
  PLProductItem,
  PLProductImage,
  PLProductName } from "./style";

const ProductsLandingScreen = (props) => {
  const { data, loading, error } = useQuery(productCategoriesLanding);

  const breadcrumbItems = [
    { name: "Home", link: "/" },
    { name: "Products", link: "/products" },
  ];

  return (
    <MainLayout>
      <PLContainer>
        <Breadcrumbs items={breadcrumbItems} /> {/* Add this line */}
        <PLHeading as="h1" font="3xl" color="primary">
          Our Products
        </PLHeading>
        <PLProductGrid>
          {renderProductCategories()}
        </PLProductGrid>
      </PLContainer>
    </MainLayout>
  );
};

export default ProductsLandingScreen;
