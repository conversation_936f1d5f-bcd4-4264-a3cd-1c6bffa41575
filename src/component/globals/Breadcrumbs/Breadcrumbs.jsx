import React from 'react';
import { Link } from 'react-router-dom';
import { BreadcrumbContainer, BreadcrumbItem, BreadcrumbLink, BreadcrumbSeparator } from './style';

const Breadcrumbs = ({ items }) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <BreadcrumbContainer>
      {items.map((item, index) => (
        <BreadcrumbItem key={index}>
          {index < items.length - 1 ? (
            <BreadcrumbLink to={item.link}>{item.name}</BreadcrumbLink>
          ) : (
            <span>{item.name}</span>
          )}
          {index < items.length - 1 && <BreadcrumbSeparator>/</BreadcrumbSeparator>}
        </BreadcrumbItem>
      ))}
    </BreadcrumbContainer>
  );
};

export default Breadcrumbs;